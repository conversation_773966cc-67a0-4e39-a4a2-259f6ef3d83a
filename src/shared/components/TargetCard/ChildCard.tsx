import { ArrowUpRightS<PERSON>re, Person, Trash } from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Container,
  Typography,
} from '@ghq-abi/design-system-v2';

import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';
import { ProposalStatusEnum } from '~/shared/utils/enums';

import { Scale } from '../icons';

import { DeliverableTypeIcon } from './DeliverableTypeIcon';
import { TargetFooter, TruncatedTitle } from './shared';

interface ChildCardProps {
  target: Target;
  disableDrag?: boolean;
  onEditTarget?: (target: Target) => void;
  onRemoveActionClick?: (target: Target[]) => void;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  showActions?: boolean;
  isOnCatalogWithTabs?: boolean;
  isInsideTargetCard?: boolean;
  disableChildActions?: boolean;
  onAcceptTarget?: (targetUid: string, agree?: boolean) => void;
  getTargetAgreeStatus?: (targetUid: string) => boolean | null;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
  showBadgesInProposalTab?: boolean;
  isAgreedTarget?: boolean;
}

export function ChildCard({
  target,
  disableDrag,
  onRemoveActionClick,
  onEditTarget,
  hasManagerPermission,
  isDrawer,
  showActions = false,
  isOnCatalogWithTabs = false,
  isInsideTargetCard = false,
  disableChildActions = false,
  onAcceptTarget,
  getTargetAgreeStatus,
  isTargetAccepted,
  isTargetLoading,
  showBadgesInProposalTab = false,
  isAgreedTarget = false,
}: ChildCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: dragging,
  } = useDraggable({
    id: target?.uid ? target.uid : 'draggable-child-card',
    data: {
      type: 'target',
      data: target,
    },
  });

  const child = target.children?.[0] ? target.children[0] : target;

  // Simplified logic: use character limits only when in catalog with tabs
  const getCharacterLimit = () => {
    if (!isOnCatalogWithTabs) {
      return undefined;
    }
    return isInsideTargetCard ? 25 : 30;
  };

  // Add badge rendering logic for read-only modes when not using TargetFooter
  const renderAgreementBadge = () => {
    // Only render badges when not using TargetFooter (isOnCatalogWithTabs = false)
    // and when we have agreement status data
    if (isOnCatalogWithTabs || !getTargetAgreeStatus || !target.uid) {
      return null;
    }

    const agreeStatus = getTargetAgreeStatus(target.uid);

    if (agreeStatus === true) {
      return (
        <Badge className="border border-green-500 text-green-500 bg-transparent">
          I Agree
        </Badge>
      );
    }

    if (agreeStatus === false) {
      return (
        <Badge className="border border-red-500 text-red-500 bg-transparent">
          Don&apos;t Agree
        </Badge>
      );
    }

    return null;
  };

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: dragging ? 0 : 1,
  };

  return (
    <div
      ref={!disableDrag ? setNodeRef : undefined}
      style={!disableDrag ? style : undefined}
      {...(!disableDrag ? listeners : {})}
      {...(!disableDrag ? attributes : {})}
      className={`
        ${!disableDrag ? 'cursor-grab' : ''}
        ${
          dragging
            ? '!w-80  shadow-2xl z-[1000] cursor-grabbing transition-all duration-500 scale-75'
            : 'w-full'
        }
        ${isAgreedTarget ? 'opacity-60' : ''}
      `}
    >
      <Card.Root round="md" className="py-2">
        {!isOnCatalogWithTabs && showActions && (
          <div className="flex w-full items-center p-[12px] justify-end z-10">
            <Button
              variant="tertiary"
              size="icon"
              disabled={disableChildActions}
              onClick={() => onEditTarget?.(child)}
            >
              <ArrowUpRightSquare />
            </Button>

            <Button
              variant="tertiary"
              size="icon"
              disabled={disableChildActions}
              onClick={() => onRemoveActionClick?.([child])}
            >
              <Trash />
            </Button>
          </div>
        )}

        <Card.Header className="flex flex-row gap-4 items-center pb-1 pt-0">
          <DeliverableTypeIcon target={target} />
          <Card.Title
            className={cn('flex flex-col max-w-4xl flex-shrink min-w-0', {
              'max-w-xs': isDrawer,
            })}
          >
            {isOnCatalogWithTabs ? (
              <TruncatedTitle
                title={child.deliverable?.name || ''}
                maxLength={getCharacterLimit()}
              />
            ) : (
              <Typography
                variant="body-md-regular"
                color="dark"
                className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis min-w-0"
              >
                {child.deliverable?.name}
              </Typography>
            )}
            <Container className="flex flex-row items-center gap-4 min-w-0 overflow-hidden">
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1 flex-shrink-0"
              >
                <Person />
                {child.deliverable?.usage || 0}
              </Typography>
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1 flex-shrink-0"
              >
                <Scale />
                {child.weight}%
              </Typography>
            </Container>
          </Card.Title>
        </Card.Header>
        <Card.Content className="flex flex-col gap-2">
          <Typography variant="metadata-sm-regular">
            {child.deliverable?.calculationMethod}
          </Typography>
          <Container className="flex flex-col">
            <Typography variant="metadata-xs-bold" color="dark">
              SCOPE:
            </Typography>
            <Typography variant="metadata-sm-regular">{child.scope}</Typography>
          </Container>
          <TargetFooter
            target={target}
            isOnCatalogWithTabs={isOnCatalogWithTabs}
            showBadgesInProposalTab={showBadgesInProposalTab}
            onAcceptTarget={onAcceptTarget}
            agreeStatus={target.uid ? getTargetAgreeStatus?.(target.uid) : null}
            isTargetAccepted={isTargetAccepted}
            isTargetLoading={isTargetLoading}
          />
          {/* Show agreement badge when not using TargetFooter */}
          {renderAgreementBadge() && (
            <Container className="flex justify-end mt-2">
              {renderAgreementBadge()}
            </Container>
          )}
        </Card.Content>
      </Card.Root>
    </div>
  );
}
