import { Badge, Card, Container } from '@ghq-abi/design-system-v2';

import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';
import { getTargetCardFooterText } from '~/shared/utils/getTargetCardFooterText';

import { TargetFooter } from './components/TargetFooter';
import { ChildCard } from './ChildCard';
import { ParentCard } from './ParentCard';
import { TargetCardProps } from './types';

export function TargetCard({
  data,
  proposalStatus,
  currentTargetType,
  hideChildren,
  onEditTarget,
  onRemoveActionClick,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  isOnDroppableArea,
  isOnCatalogWithTabs = false,
  onAcceptTarget,
  getTargetAgreeStatus,
  isTargetAccepted,
  isTargetLoading,
  showBadgesInProposalTab = false,
  isTargetAgreed,
}: TargetCardProps) {
  const footerText = getTargetCardFooterText(
    proposalStatus,
    currentTargetType,
    data,
  );

  const filteredChildren = currentTargetType
    ? data.children?.filter(child =>
        child.targetTypes?.some(
          targetType => targetType.type === currentTargetType,
        ),
      )
    : data.children;

  const renderFooterBadge = () => {
    // In read-only mode (COMPLETED or IN_PROGRESS_FINAL), use actual agreement status from API
    const isReadOnlyMode =
      proposalStatus === ProposalStatusEnum.COMPLETED ||
      proposalStatus === ProposalStatusEnum.IN_PROGRESS_FINAL;

    if (data.uid && getTargetAgreeStatus) {
      const agreeStatus = getTargetAgreeStatus(data.uid);

      if (agreeStatus === true) {
        return (
          <Badge className="border border-green-500 text-green-500 bg-transparent">
            I Agree
          </Badge>
        );
      }

      if (agreeStatus === false) {
        return (
          <Badge className="border border-red-500 text-red-500 bg-transparent">
            Don&apos;t Agree
          </Badge>
        );
      }
    }

    // Fallback to static footer text logic for non-read-only modes
    if (!footerText) {
      return null;
    }

    const isAgree = footerText === 'I Agree';
    const isDisagree = footerText === "Don't agree";

    if (isAgree) {
      return (
        <Badge className="border border-green-500 text-green-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    if (isDisagree) {
      return (
        <Badge className="border border-red-500 text-red-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    return null;
  };

  const renderFooterContent = () => {
    if (isOnCatalogWithTabs) {
      return (
        <TargetFooter
          target={data}
          isOnCatalogWithTabs={isOnCatalogWithTabs}
          showBadgesInProposalTab={showBadgesInProposalTab}
          onAcceptTarget={onAcceptTarget}
          agreeStatus={data.uid ? getTargetAgreeStatus?.(data.uid) : null}
          isTargetAccepted={isTargetAccepted}
          isTargetLoading={isTargetLoading}
        />
      );
    }

    return (
      <Container className="flex justify-end">{renderFooterBadge()}</Container>
    );
  };

  return (
    <Card.Root round="md">
      <ParentCard
        data={data}
        onRemoveActionClick={onRemoveActionClick}
        isDrawer={isDrawer}
        isOnCatalogWithTabs={isOnCatalogWithTabs}
        isTargetAgreed={isTargetAgreed ? isTargetAgreed(data) : false}
      />
      {!hideChildren && (
        <Card.Content className="flex flex-col gap-4">
          {filteredChildren?.map(child => {
            const shouldDisableChildActions = () => {
              if (isTargetAgreed && isTargetAgreed(child)) {
                return true;
              }

              const childTargetTypes =
                child.targetTypes?.map(tt => tt.type) || [];

              if (currentTargetType === TargetTypeEnum.FEEDBACK) {
                return childTargetTypes.includes(TargetTypeEnum.PROPOSAL);
              } else if (currentTargetType === TargetTypeEnum.FINAL) {
                return (
                  childTargetTypes.includes(TargetTypeEnum.PROPOSAL) ||
                  childTargetTypes.includes(TargetTypeEnum.FEEDBACK)
                );
              }

              return false;
            };

            return (
              <ChildCard
                key={child.uid}
                target={child}
                disableDrag={true}
                onEditTarget={onEditTarget}
                onRemoveActionClick={onRemoveActionClick}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer={isDrawer}
                showActions={isOnDroppableArea}
                isOnCatalogWithTabs={isOnCatalogWithTabs}
                isInsideTargetCard={true}
                disableChildActions={shouldDisableChildActions()}
                getTargetAgreeStatus={getTargetAgreeStatus}
              />
            );
          })}
          {renderFooterContent()}
        </Card.Content>
      )}
    </Card.Root>
  );
}
