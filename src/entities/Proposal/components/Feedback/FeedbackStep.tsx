import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { useAgreeStatus } from '~/shared/hooks/useAgreeStatus';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

import { FeedbackDrag } from './FeedbackDrag';

interface FeedbackStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
}

export function FeedbackStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  onProposalUpdate,
}: FeedbackStepProps) {
  const feedbackTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FEEDBACK,
    ),
  );

  const { getTargetAgreeStatus } = useAgreeStatus({
    targets: feedbackTargets,
    currentTargetType: TargetTypeEnum.FEEDBACK,
    enabled: proposalStatus === ProposalStatusEnum.COMPLETED,
  });

  if (
    feedbackTargets.length === 0 &&
    hasEmployeePermission &&
    proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FEEDBACK
  ) {
    return (
      <TabEmptyState
        title="No feedback targets available"
        description="There are no feedback targets to display at this time."
      />
    );
  }

  return (
    <>
      {proposalStatus === ProposalStatusEnum.IN_PROGRESS_FEEDBACK &&
      hasEmployeePermission ? (
        <FeedbackDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={feedbackTargets}
          allTargets={targets}
          hasManagerPermission={!!hasManagerPermission}
          onProposalUpdate={onProposalUpdate}
        />
      ) : (proposalStatus !== ProposalStatusEnum.NOT_STARTED &&
          proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FEEDBACK) ||
        hasManagerPermission ? (
        <Container className="flex flex-col gap-4">
          {feedbackTargets.map(target => {
            if (target.children && target.children.length > 1) {
              return (
                <TargetCard
                  key={target.uid}
                  data={target}
                  proposalStatus={proposalStatus}
                  hideChildren={target.children.length <= 1}
                  currentTargetType={TargetTypeEnum.FEEDBACK}
                  hasManagerPermission={hasManagerPermission}
                  hasEmployeePermission={hasEmployeePermission}
                  isDrawer={isDrawer}
                  isOnCatalogWithTabs={
                    proposalStatus === ProposalStatusEnum.COMPLETED
                  }
                  getTargetAgreeStatus={
                    proposalStatus === ProposalStatusEnum.COMPLETED
                      ? getTargetAgreeStatus
                      : undefined
                  }
                />
              );
            }
            const targetTypes = target.targetTypes?.map(tt => tt.type) || [];
            const shouldDisableActions = targetTypes.includes(
              TargetTypeEnum.PROPOSAL,
            );

            return (
              <ChildCard
                key={target.uid}
                target={target}
                disableDrag={true}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer={isDrawer}
                disableChildActions={shouldDisableActions}
                isOnCatalogWithTabs={
                  proposalStatus === ProposalStatusEnum.COMPLETED
                }
                getTargetAgreeStatus={
                  proposalStatus === ProposalStatusEnum.COMPLETED
                    ? getTargetAgreeStatus
                    : undefined
                }
              />
            );
          })}
        </Container>
      ) : (
        <FeedbackDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={feedbackTargets}
          hasManagerPermission={!!hasManagerPermission}
          onProposalUpdate={onProposalUpdate}
        />
      )}
    </>
  );
}
