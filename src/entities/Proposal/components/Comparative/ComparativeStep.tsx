import React from 'react';
import { Container, Typography } from '@ghq-abi/design-system-v2';

import { TargetCard } from '~/shared/components/TargetCard';
import { useAgreeStatus } from '~/shared/hooks/useAgreeStatus';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';
import { TabEmptyState } from '../TabEmptyState';

interface ComparativeStepProps {
  targets: Target[];
  proposalStatus?: ProposalStatusEnum;
}

export function ComparativeStep({
  targets,
  proposalStatus,
}: ComparativeStepProps) {
  const proposalTargets = filterTargetsByType(targets, TargetTypeEnum.PROPOSAL);
  const feedbackTargets = filterTargetsByType(targets, TargetTypeEnum.FEEDBACK);
  const finalTargets = filterTargetsByType(targets, TargetTypeEnum.FINAL);

  const { getTargetAgreeStatus: getFinalAgreeStatus } = useAgreeStatus({
    targets: finalTargets,
    currentTargetType: TargetTypeEnum.FINAL,
    enabled: finalTargets.length > 0,
  });

  const { getTargetAgreeStatus: getProposalAgreeStatus } = useAgreeStatus({
    targets: proposalTargets,
    currentTargetType: TargetTypeEnum.PROPOSAL,
    enabled: proposalTargets.length > 0,
  });

  const { getTargetAgreeStatus: getFeedbackAgreeStatus } = useAgreeStatus({
    targets: feedbackTargets,
    currentTargetType: TargetTypeEnum.FEEDBACK,
    enabled: feedbackTargets.length > 0,
  });

  const hasAnyTargets =
    proposalTargets.length > 0 ||
    feedbackTargets.length > 0 ||
    finalTargets.length > 0;

  if (!hasAnyTargets) {
    return (
      <TabEmptyState
        title="No data available"
        description="There are no targets to compare at this time."
      />
    );
  }

  return (
    <Container className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      <Container className="flex flex-col gap-4">
        <Typography
          variant="title-sm-bold"
          className="text-center pb-2 border-b border-gray-200"
        >
          Proposals
        </Typography>
        {proposalTargets.length === 0 ? (
          <Container className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
            <Typography variant="body-sm-regular">
              No proposals available
            </Typography>
          </Container>
        ) : (
          <Container className="flex flex-col gap-4">
            {proposalTargets.map(target => (
              <TargetCard
                key={`proposal-${target.uid}`}
                data={target}
                proposalStatus={proposalStatus}
                currentTargetType={TargetTypeEnum.PROPOSAL}
                isOnCatalogWithTabs
                getTargetAgreeStatus={getProposalAgreeStatus}
              />
            ))}
          </Container>
        )}
      </Container>

      <Container className="flex flex-col gap-4">
        <Typography
          variant="title-sm-bold"
          className="text-center pb-2 border-b border-gray-200"
        >
          Feedback
        </Typography>
        {feedbackTargets.length === 0 ? (
          <Container className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
            <Typography variant="body-sm-regular">
              No feedback available
            </Typography>
          </Container>
        ) : (
          <Container className="flex flex-col gap-4">
            {feedbackTargets.map(target => (
              <TargetCard
                key={`feedback-${target.uid}`}
                data={target}
                proposalStatus={proposalStatus}
                currentTargetType={TargetTypeEnum.FEEDBACK}
                isOnCatalogWithTabs
                getTargetAgreeStatus={getFeedbackAgreeStatus}
              />
            ))}
          </Container>
        )}
      </Container>

      <Container className="flex flex-col gap-4">
        <Typography
          variant="title-sm-bold"
          className="text-center pb-2 border-b border-gray-200"
        >
          Final
        </Typography>
        {finalTargets.length === 0 ? (
          <Container className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
            <Typography variant="body-sm-regular">
              No final targets available
            </Typography>
          </Container>
        ) : (
          <Container className="flex flex-col gap-4">
            {finalTargets.map(target => (
              <TargetCard
                key={`final-${target.uid}`}
                data={target}
                proposalStatus={proposalStatus}
                currentTargetType={TargetTypeEnum.FINAL}
                isOnCatalogWithTabs
                getTargetAgreeStatus={getFinalAgreeStatus}
                showBadgesInProposalTab={true}
              />
            ))}
          </Container>
        )}
      </Container>
    </Container>
  );
}
