import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Container,
  Drawer,
  Separator,
  Skeleton,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';

import { useProposal } from '~/entities/Home/hooks/useProposal';
import { Avatar, Pagination } from '~/shared/components';
import { FormikSelect } from '~/shared/components/FormikSelect';
import { FormikTextarea } from '~/shared/components/FormikTextarea';
import { SimpleDeliverableCard } from '~/shared/components/SimpleDeliverableCard';
import { usePagination } from '~/shared/hooks';
import proposalService from '~/shared/services/proposal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Employee } from '~/shared/types/Employee';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { WEIGHT_OPTIONS } from '../../constants';
import {
  CreateEditTargetBody,
  CreateTargetDrawerProps,
  FormValues,
} from '../../types';

const EmployeeCard = ({ employee }: { employee: Employee | undefined }) => {
  if (!employee) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-4">
        <Avatar
          name={employee?.name}
          globalId={`${employee?.globalId}`}
          size={30}
        />
        <div className="flex flex-col">
          <Typography variant="body-sm-bold">{employee?.name}</Typography>
          <Typography variant="metadata-sm-regular" color="light">
            {employee?.businessFunction}
          </Typography>
        </div>
      </div>
    </div>
  );
};

export const CreateTargetDrawer = ({
  proposalId,
  isOpen,
  onClose,
  isEdit = false,
  data,
  onSuccessSubmit,
  targetType = TargetTypeEnum.PROPOSAL,
  className,
  isDraggingCatalog = false,
  filters,
}: CreateTargetDrawerProps) => {
  const queryClient = useQueryClient();
  const { t } = useTranslate();
  const [selectedProposals, setSelectedProposals] = useState<{
    isDraggingProposal: boolean;
    uids: string[];
  }>({
    isDraggingProposal: false,
    uids: [],
  });

  const initialValues = {
    definition: isEdit
      ? (data as Target)?.deliverable?.definition || ''
      : (data as DeliverableItem)?.definition || '',
    calculationMethod: isEdit
      ? (data as Target)?.deliverable?.calculationMethod || ''
      : (data as DeliverableItem)?.calculationMethod || '',
    weight: isEdit ? (data as Target)?.weight || 5 : 5,
    scope: isEdit ? (data as Target)?.scope || '' : '',
  };

  const validationSchema = Yup.object({
    weight: Yup.string().required(
      `${t('common_weight')} ${t('common_is_required')}`,
    ),
    scope: Yup.string().required(
      `${t('common_scope')} ${t('common_is_required')}`,
    ),
  });

  const { isLoading, mutate } = useMutation({
    mutationFn: async (values: object) => {
      if (
        selectedProposals.isDraggingProposal &&
        selectedProposals.uids.length > 0
      ) {
        const allProposalIds = [proposalId, ...selectedProposals.uids];
        const promises = allProposalIds.map(uid =>
          proposalService.createTarget(uid, values),
        );
        return Promise.all(promises);
      }

      return proposalService.createTarget(proposalId, values);
    },
    onSuccess: async (proposal: Proposal | Proposal[]) => {
      onSuccessSubmit(proposal);
      onClose();
      await queryClient.invalidateQueries({ queryKey: ['proposals'] });
      await queryClient.fetchQuery({
        queryKey: ['proposals', newFilters],
        queryFn: () =>
          proposalService.getProposals({
            ...newFilters,
            pageNumber: 1,
            pageSize: 9,
          }),
      });
      setSelectedProposals({
        isDraggingProposal: false,
        uids: [],
      });
    },
    onError: error => {
      setSelectedProposals({
        isDraggingProposal: false,
        uids: [],
      });
      console.error(error);
    },
  });

  const { isLoading: isLoadingEdit, mutate: mutateEdit } = useMutation({
    mutationFn: (values: object) => {
      return proposalService.editTarget(proposalId, {
        ...values,
      });
    },
    onSuccess: (proposal: Proposal) => {
      onSuccessSubmit(proposal);
      onClose();
    },
    onError: error => {
      console.error(error);
    },
  });

  const { totalPages, currentPage, setPageNumber, pageSize } = usePagination({
    initialPageSize: 9,
    totalRecords: 0,
  });

  const newFilters = {
    ...filters,
    status: [
      ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
      ProposalStatusEnum.IN_PROGRESS_PROPOSAL,
      ProposalStatusEnum.NOT_STARTED,
    ],
  };

  const { proposals, isInitialLoading, isError } = useProposal(
    currentPage,
    pageSize,
    undefined,
    newFilters,
  );

  const actualTotalPages = proposals?.totalRecords
    ? Math.ceil(proposals.totalRecords / pageSize)
    : totalPages;

  const createTargetBody = (values: FormValues): CreateEditTargetBody => {
    const baseTarget = {
      weight: Number(values.weight),
      scope: values.scope,
      targetType: targetType,
    };

    return {
      targets: [
        {
          ...baseTarget,
          uidDeliverable: isEdit
            ? (data as Target)?.deliverable?.uid || ''
            : (data as DeliverableItem).uid || '',
        },
      ],
    };
  };

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content
        className={cn('w-full max-w-lg pt-12 h-[calc(100%-74px)]', className)}
      >
        <Drawer.Title hidden />
        <Drawer.Description hidden />
        <Formik<FormValues>
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={async values => {
            if (isEdit) {
              const editTargetBody: CreateEditTargetBody = {
                targets: [
                  {
                    uid: (data as Target).uid,
                    weight: Number(values.weight),
                    scope: values.scope,
                  },
                ],
              };

              mutateEdit(editTargetBody);
            } else {
              const targetBody = createTargetBody(values);
              mutate(targetBody);
            }
          }}
        >
          {({ submitForm, handleChange, values }) => {
            const deliverableType = isEdit
              ? (data as Target)?.deliverable?.type
              : (data as DeliverableItem)?.type;

            return (
              <>
                <Container className="overflow-y-auto h-full">
                  <Container className="flex justify-between items-center p-4">
                    <Container className="flex flex-col">
                      <Typography variant="body-sm-bold">
                        {t('common_create_target')}
                      </Typography>
                    </Container>
                  </Container>
                  <Separator />
                  <Container className="flex flex-col py-4 px-2 gap-4">
                    <SimpleDeliverableCard
                      name={
                        isEdit
                          ? (data as Target)?.deliverable?.name || 'N/A'
                          : (data as DeliverableItem)?.name || 'N/A'
                      }
                      businessFunction={
                        isEdit
                          ? (data as Target)?.deliverable?.businessFunction ||
                            'N/A'
                          : (data as DeliverableItem)?.businessFunction || 'N/A'
                      }
                      usage={
                        isEdit
                          ? (data as Target)?.deliverable?.usage || 0
                          : (data as DeliverableItem)?.usage || 0
                      }
                      deliverableType={
                        isEdit
                          ? (data as Target)?.deliverable?.deliverableType?.code
                          : (data as DeliverableItem)?.deliverableType?.code
                      }
                    />
                    <Form>
                      {!isDraggingCatalog && (
                        <>
                          <FormikTextarea
                            label="Definition"
                            name="definition"
                            value={values.definition}
                            onChange={handleChange}
                            placeholder="Insert here..."
                            disabled
                          />
                          {deliverableType !== 'PROJECT' && (
                            <FormikTextarea
                              label="Calculation Method"
                              name="calculationMethod"
                              value={values.calculationMethod}
                              onChange={handleChange}
                              placeholder="Insert here..."
                              disabled
                            />
                          )}
                        </>
                      )}
                      <FormikSelect
                        label="Weight"
                        name="weight"
                        options={WEIGHT_OPTIONS}
                        value={`${values.weight}`}
                        onChange={handleChange}
                        placeholder="Insert here..."
                      />
                      <FormikTextarea
                        label="Scope"
                        name="scope"
                        value={values.scope}
                        onChange={handleChange}
                        placeholder="Insert here..."
                      />
                    </Form>
                  </Container>
                  {isDraggingCatalog && (
                    <Container className="flex flex-col py-4 px-2 gap-4">
                      <Typography variant="body-sm-bold" className="mb-4">
                        {t('common_select_proposals_to_assign')}
                      </Typography>
                      {isError && (
                        <Container className="flex justify-center items-center py-8">
                          <Typography variant="metadata-sm-medium">
                            {t('common_something_went_wrong')}
                          </Typography>
                        </Container>
                      )}
                      {proposals && !isInitialLoading
                        ? proposals?.data?.map(target => (
                            <Container
                              key={target.uid}
                              className="flex items-center gap-2 border-b-[1px] border-b-gray-200 pb-4"
                            >
                              {target && (
                                <div className="flex items-center gap-4">
                                  <input
                                    type="checkbox"
                                    key={target.uid}
                                    onClick={() =>
                                      setSelectedProposals({
                                        isDraggingProposal: true,
                                        uids: [
                                          ...selectedProposals.uids,
                                          target?.uid || '',
                                        ],
                                      })
                                    }
                                  />
                                  <EmployeeCard employee={target.employee} />
                                </div>
                              )}
                            </Container>
                          ))
                        : Array.from({ length: 9 }).map((_, index) => (
                            <Skeleton key={index} className="w-full h-[70px]" />
                          ))}
                      <Pagination
                        currentPage={currentPage}
                        totalPages={actualTotalPages}
                        onPageChange={setPageNumber}
                      />
                    </Container>
                  )}
                </Container>
                <Separator />
                <Container className="flex flex-row justify-end p-4 gap-4 w-full">
                  <Button
                    variant="secondary"
                    border="default"
                    disabled={isLoading}
                    onClick={onClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    disabled={isLoading || isLoadingEdit}
                    isLoading={isLoading || isLoadingEdit}
                    onClick={submitForm}
                  >
                    Assign
                  </Button>
                </Container>
              </>
            );
          }}
        </Formik>
      </Drawer.Content>
    </Drawer.Root>
  );
};
