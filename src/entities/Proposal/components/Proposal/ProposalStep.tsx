import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { useAgreeStatus } from '~/shared/hooks/useAgreeStatus';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';
import { TabEmptyState } from '../TabEmptyState';

import { ProposalDrag } from './ProposalDrag';

interface ProposalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
  isLoading?: boolean;
}

export function ProposalStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  onProposalUpdate,
  isLoading,
}: ProposalStepProps) {
  const proposalTargets = filterTargetsByType(targets, TargetTypeEnum.PROPOSAL);

  const { getTargetAgreeStatus } = useAgreeStatus({
    targets: proposalTargets,
    currentTargetType: TargetTypeEnum.PROPOSAL,
    enabled:
      proposalStatus === ProposalStatusEnum.COMPLETED ||
      proposalStatus === ProposalStatusEnum.IN_PROGRESS_FINAL,
  });
  if (
    proposalTargets.length === 0 &&
    hasEmployeePermission &&
    !hasManagerPermission
  ) {
    return (
      <TabEmptyState
        title="No proposals available"
        description="There are no proposal targets to display at this time."
      />
    );
  }

  const STATUS_IN_PROGRESS_MAP = [
    ProposalStatusEnum.IN_PROGRESS_PROPOSAL,
    ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
    ProposalStatusEnum.IN_PROGRESS_FINAL,
  ];

  const isShowActions =
    hasManagerPermission &&
    !STATUS_IN_PROGRESS_MAP.includes(proposalStatus as ProposalStatusEnum);

  return (
    <>
      {(proposalStatus === ProposalStatusEnum.NOT_STARTED ||
        proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL) &&
      hasManagerPermission ? (
        <ProposalDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={proposalTargets}
          onProposalUpdate={onProposalUpdate}
          isLoading={isLoading}
        />
      ) : (
        <Container className="flex flex-col gap-4">
          {proposalTargets.map(target => {
            if (target.children && target.children.length > 1) {
              return (
                <TargetCard
                  key={target.uid}
                  data={target}
                  proposalStatus={proposalStatus}
                  hideChildren={target.children.length <= 1}
                  currentTargetType={TargetTypeEnum.PROPOSAL}
                  hasManagerPermission={hasManagerPermission}
                  hasEmployeePermission={hasEmployeePermission}
                  isDrawer={isDrawer}
                  getTargetAgreeStatus={
                    proposalStatus === ProposalStatusEnum.COMPLETED ||
                    proposalStatus === ProposalStatusEnum.IN_PROGRESS_FINAL
                      ? getTargetAgreeStatus
                      : undefined
                  }
                />
              );
            }
            return (
              <ChildCard
                key={target.uid}
                target={target}
                disableDrag={true}
                hasManagerPermission={hasManagerPermission}
                hasEmployeePermission={hasEmployeePermission}
                isDrawer={isDrawer}
                showActions={isShowActions}
                getTargetAgreeStatus={
                  proposalStatus === ProposalStatusEnum.COMPLETED ||
                  proposalStatus === ProposalStatusEnum.IN_PROGRESS_FINAL
                    ? getTargetAgreeStatus
                    : undefined
                }
              />
            );
          })}
        </Container>
      )}
    </>
  );
}
