import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

import { FinalDrag } from './FinalDrag';

interface FinalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  onProposalUpdate?: (updatedProposal: Proposal) => void;
  onOpenTargetComments?: (targetId: string) => void;
}

export function FinalStep({
  targets,
  proposalUid,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  onProposalUpdate,
  onOpenTargetComments,
}: FinalStepProps) {
  const user = JSON.parse(localStorage.getItem('user') ?? '{}');
  const isGlobalAdmin =
    user.roles_json.includes('ADMINISTRATOR') && user.str_zone === 'GLOBAL';

  const finalTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FINAL,
    ),
  );

  if (
    finalTargets.length === 0 &&
    hasManagerPermission &&
    proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FINAL &&
    !(proposalStatus === ProposalStatusEnum.COMPLETED && isGlobalAdmin)
  ) {
    return (
      <TabEmptyState
        title="No final targets available"
        description="There are no final targets to display at this time."
      />
    );
  }

  const shouldEnableEditing =
    (proposalStatus === ProposalStatusEnum.IN_PROGRESS_FINAL &&
      hasManagerPermission) ||
    (proposalStatus === ProposalStatusEnum.COMPLETED &&
      isGlobalAdmin &&
      hasManagerPermission);

  return (
    <>
      {shouldEnableEditing ? (
        <FinalDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={finalTargets}
          allTargets={targets}
          onProposalUpdate={onProposalUpdate}
        />
      ) : (proposalStatus !== ProposalStatusEnum.NOT_STARTED &&
          proposalStatus !== ProposalStatusEnum.IN_PROGRESS_PROPOSAL &&
          proposalStatus !== ProposalStatusEnum.IN_PROGRESS_FEEDBACK) ||
        hasEmployeePermission ? (
        <Container className="flex flex-col gap-4">
          {finalTargets.length === 0 ? (
            <TabEmptyState
              title="No final targets available"
              description="There are no final targets to display at this time."
            />
          ) : (
            finalTargets.map(target => {
              if (target.children && target.children.length > 1) {
                return (
                  <TargetCard
                    key={target.uid}
                    data={target}
                    proposalStatus={proposalStatus}
                    hideChildren={target.children.length <= 1}
                    currentTargetType={TargetTypeEnum.FINAL}
                    hasManagerPermission={hasManagerPermission}
                    hasEmployeePermission={hasEmployeePermission}
                  />
                );
              }
              const targetTypes = target.targetTypes?.map(tt => tt.type) || [];
              const shouldDisableActions =
                targetTypes.includes(TargetTypeEnum.PROPOSAL) ||
                targetTypes.includes(TargetTypeEnum.FEEDBACK);

              return (
                <ChildCard
                  key={target.uid}
                  target={target}
                  disableDrag={true}
                  hasManagerPermission={hasManagerPermission}
                  hasEmployeePermission={hasEmployeePermission}
                  disableChildActions={shouldDisableActions}
                />
              );
            })
          )}
        </Container>
      ) : (
        <TabEmptyState
          title="Nothing to see here yet"
          description="This page will be available for completion after the employee submits feedback."
        />
      )}
    </>
  );
}
